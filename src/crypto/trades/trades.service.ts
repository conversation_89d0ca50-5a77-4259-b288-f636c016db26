import { Injectable } from '@nestjs/common';
import { QuidaxService } from '@app/quidax';
import { TradeRepository } from '../repositories/trades.repository';
import { Trade } from '../entities/trades.entity';
import { AuthData } from '@crednet/authmanager';

@Injectable()
export class TradesService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly tradesRepository: TradeRepository,
  ) {}
  async fetchRecentTradesForAMarket(
    baseCurrency: string,
    quoteCurrency: string,
  ): Promise<any> {
    const marketPair = `${baseCurrency}${quoteCurrency}`;
    const { data } =
      await this.quidaxService.fetchRecentTradesForAMarket(marketPair);
    return data;
  }

  async fetchAllTradesForUserForAMarketPair(
    auth: AuthData,
    marketId: string,
  ): Promise<Trade[]> {
    const userId = auth.id;
    return this.tradesRepository.find({
      where: { user: { userId: userId }, market_id: marketId },
    });
  }
}
